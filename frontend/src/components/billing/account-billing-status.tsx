'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { PricingSection } from '@/components/home/<USER>/pricing-section';
import { isLocalMode } from '@/lib/config';
import {
  getSubscription,
  createPortalSession,
  SubscriptionStatus,
} from '@/lib/api';
import { useAuth } from '@/components/AuthProvider';
import { Skeleton } from '@/components/ui/skeleton';

type Props = {
  accountId: string;
  returnUrl: string;
};

export default function AccountBillingStatus({ accountId, returnUrl }: Props) {
  const { session, isLoading: authLoading } = useAuth();
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isManaging, setIsManaging] = useState(false);

  useEffect(() => {
    async function fetchSubscription() {
      if (authLoading || !session) return;

      try {
        const data = await getSubscription();
        setSubscriptionData(data);
        setError(null);
      } catch (err) {
        console.error('Failed to get subscription:', err);
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to load subscription data',
        );
      } finally {
        setIsLoading(false);
      }
    }

    fetchSubscription();
  }, [session, authLoading]);

  const handleManageSubscription = async () => {
    try {
      setIsManaging(true);
      const { url } = await createPortalSession({ return_url: returnUrl });
      window.location.href = url;
    } catch (err) {
      console.error('Failed to create portal session:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to create portal session',
      );
    } finally {
      setIsManaging(false);
    }
  };

  // In local development mode, show a simplified component
  if (isLocalMode()) {
    return (
      <div className="rounded-xl border shadow-sm bg-card p-6">
        <h2 className="text-xl font-semibold mb-4">Billing Status</h2>
        <div className="p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center">
          <p className="text-sm text-muted-foreground">
            Running in local development mode - billing features are disabled
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            Agent usage limits are not enforced in this environment
          </p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading || authLoading) {
    return (
      <div className="rounded-xl border shadow-sm bg-card p-6">
        <h2 className="text-xl font-semibold mb-4">Billing Status</h2>
        <div className="space-y-4">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="rounded-xl border shadow-sm bg-card p-6">
        <h2 className="text-xl font-semibold mb-4">Billing Status</h2>
        <div className="p-4 mb-4 bg-destructive/10 border border-destructive/20 rounded-lg text-center">
          <p className="text-sm text-destructive">
            Error loading billing status: {error}
          </p>
        </div>
      </div>
    );
  }

  const isPlan = (planId?: string) => {
    return subscriptionData?.plan_name === planId;
  };

  const planName = isPlan('free')
    ? 'Free'
    : isPlan('base')
      ? 'Pro'
      : isPlan('extra')
        ? 'Enterprise'
        : 'Unknown';

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Facturation et Abonnement</h2>
        <p className="text-muted-foreground">
          Gérez votre abonnement et suivez votre utilisation d'Alex
        </p>
      </div>

      {/* Usage Stats Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-xl border bg-card p-6 text-center">
          <div className="text-2xl font-bold text-primary mb-2">
            {subscriptionData?.current_usage?.toFixed(0) || '0'}
          </div>
          <div className="text-sm text-muted-foreground mb-1">
            Crédits utilisés ce mois
          </div>
          <div className="text-xs text-muted-foreground">
            sur {subscriptionData?.minutes_limit || '30'} disponibles
          </div>
        </div>

        <div className="rounded-xl border bg-card p-6 text-center">
          <div className="text-2xl font-bold text-green-600 mb-2">
            {subscriptionData?.minutes_limit ?
              Math.max(0, subscriptionData.minutes_limit - (subscriptionData.current_usage || 0)).toFixed(0)
              : '30'}
          </div>
          <div className="text-sm text-muted-foreground mb-1">
            Crédits restants
          </div>
          <div className="text-xs text-muted-foreground">
            jusqu'au prochain cycle
          </div>
        </div>

        <div className="rounded-xl border bg-card p-6 text-center">
          <div className="text-2xl font-bold text-blue-600 mb-2">
            {subscriptionData?.plan_name || 'Découverte'}
          </div>
          <div className="text-sm text-muted-foreground mb-1">
            Forfait actuel
          </div>
          <div className="text-xs text-muted-foreground">
            {subscriptionData?.status === 'active' ? 'Actif' : 'Gratuit'}
          </div>
        </div>
      </div>

      {/* Plans Comparison Section */}
      <div className="rounded-xl border bg-card p-6">
        <div className="mb-6 text-center">
          <h3 className="text-xl font-semibold mb-2">Choisissez votre forfait</h3>
          <p className="text-muted-foreground">
            Améliorez votre forfait pour profiter de plus de fonctionnalités avec Alex
          </p>
        </div>
        <PricingSection returnUrl={returnUrl} showTitleAndTabs={false} insideDialog={true} />
      </div>

      {/* Management Section */}
      {subscriptionData && (
        <div className="rounded-xl border bg-card p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">Gestion de l'abonnement</h3>
          <p className="text-muted-foreground mb-4">
            Accédez au portail Stripe pour gérer votre abonnement, vos factures et vos moyens de paiement
          </p>
          <Button
            onClick={handleManageSubscription}
            disabled={isManaging}
            className="bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg transition-all"
          >
            {isManaging ? 'Chargement...' : 'Gérer l\'abonnement'}
          </Button>
        </div>
      )}
    </div>
  );
}
